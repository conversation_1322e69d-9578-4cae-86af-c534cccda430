import { NextRequest, NextResponse } from 'next/server';
import { getQRCodeByDocumentId, incrementQRCodeScanCount } from '@/lib/database';

// GET /api/qr-codes/[documentId] - Get QR code information for a document
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ documentId: string }> }
) {
  try {
    const { documentId } = await params;
    const { searchParams } = new URL(request.url);
    const documentType = searchParams.get('type') as 'notification' | 'archive' | 'validation';

    if (!documentId || !documentType) {
      return NextResponse.json(
        { error: 'Document ID and type are required' },
        { status: 400 }
      );
    }

    if (!['notification', 'archive', 'validation'].includes(documentType)) {
      return NextResponse.json(
        { error: 'Document type must be "notification", "archive", or "validation"' },
        { status: 400 }
      );
    }

    const qrCode = await getQRCodeByDocumentId(documentId, documentType);

    if (!qrCode) {
      return NextResponse.json(
        { error: 'QR code not found for this document' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      qrCode: {
        id: qrCode.id,
        document_id: qrCode.document_id,
        document_type: qrCode.document_type,
        validation_url: qrCode.validation_url,
        generated_at: qrCode.generated_at,
        scan_count: qrCode.scan_count,
        last_scanned_at: qrCode.last_scanned_at,
        is_active: qrCode.is_active
      }
    });
  } catch (error) {
    console.error('Error retrieving QR code:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve QR code information' },
      { status: 500 }
    );
  }
}

// POST /api/qr-codes/[documentId] - Increment scan count for a QR code
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ documentId: string }> }
) {
  try {
    const { documentId } = await params;
    const { documentType } = await request.json();

    if (!documentId || !documentType) {
      return NextResponse.json(
        { error: 'Document ID and type are required' },
        { status: 400 }
      );
    }

    if (documentType !== 'notification' && documentType !== 'archive') {
      return NextResponse.json(
        { error: 'Document type must be either "notification" or "archive"' },
        { status: 400 }
      );
    }

    const success = await incrementQRCodeScanCount(documentId, documentType);

    if (!success) {
      return NextResponse.json(
        { error: 'QR code not found or failed to update scan count' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Scan count incremented successfully'
    });
  } catch (error) {
    console.error('Error incrementing QR code scan count:', error);
    return NextResponse.json(
      { error: 'Failed to increment scan count' },
      { status: 500 }
    );
  }
}
