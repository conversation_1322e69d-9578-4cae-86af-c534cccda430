import { NextRequest, NextResponse } from 'next/server';
import { verifyDocumentOnline } from '@/lib/supabase';

// GET /api/validate-online/[id] - Verify document online via Supabase
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Document ID is required' },
        { status: 400 }
      );
    }

    // Verify document online using Supabase
    const verification = await verifyDocumentOnline(id);

    if (!verification.isValid) {
      return NextResponse.json({
        success: false,
        isValid: false,
        error: verification.error || 'Document verification failed'
      }, { status: 404 });
    }

    // Return successful verification
    return NextResponse.json({
      success: true,
      isValid: true,
      document: {
        id: verification.document?.document_id,
        type: verification.document?.document_type,
        validatedAt: new Date().toISOString(),
        scanCount: verification.document?.scan_count,
        generatedAt: verification.document?.generated_at
      },
      message: 'Document verified successfully online'
    });
  } catch (error) {
    console.error('Error validating document online:', error);
    return NextResponse.json(
      { 
        success: false, 
        isValid: false,
        error: 'Failed to validate document online',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
