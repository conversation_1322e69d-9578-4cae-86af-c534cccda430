import { NextRequest, NextResponse } from 'next/server';
import { getQRValidationDocument } from '@/lib/database';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Get the QR validation document from the database
    const document = await getQRValidationDocument(id);

    if (!document) {
      return NextResponse.json(
        {
          success: false,
          error: 'Document not found or has not been approved',
          message: 'This document ID does not exist in our approved records.'
        },
        { status: 404 }
      );
    }

    // Return only the necessary information for validation
    const validationData = {
      id: document.id,
      template_name: document.template_name,
      applicant_name: document.applicant_name,
      barangay: document.barangay,
      approved_at: document.approved_at,
      approved_by: document.approved_by,
      ctc_number: document.ctc_number,
      or_number: document.or_number,
    };

    return NextResponse.json({
      success: true,
      document: validationData,
      message: 'Document is valid and approved'
    });

  } catch (error) {
    console.error('Document validation API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Validation service temporarily unavailable',
        message: 'Please try again later or contact support.'
      },
      { status: 500 }
    );
  }
}
