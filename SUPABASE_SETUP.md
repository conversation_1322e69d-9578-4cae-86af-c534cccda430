# Supabase Integration Setup for LDIS

This guide will help you set up Supabase integration for online QR code verification in your LDIS system.

## Prerequisites

1. A Supabase account (sign up at [supabase.com](https://supabase.com))
2. A Supabase project created

## Step 1: Create Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign in
2. Click "New Project"
3. Choose your organization
4. Enter project name (e.g., "LDIS-QR-Verification")
5. Enter a secure database password
6. Choose a region close to your users
7. Click "Create new project"

## Step 2: Get Project Credentials

1. In your Supabase dashboard, go to **Settings** > **API**
2. Copy the following values:
   - **Project URL** (e.g., `https://your-project-id.supabase.co`)
   - **Anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)

## Step 3: Configure Environment Variables

1. Create a `.env.local` file in your project root (if it doesn't exist)
2. Add your Supabase credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Important:** Replace the example values with your actual Supabase project credentials.

## Step 4: Create Database Table

1. In your Supabase dashboard, go to **SQL Editor**
2. Copy the contents of `supabase-setup.sql` file
3. Paste it into the SQL Editor
4. Click "Run" to execute the SQL

This will create:
- `qr_codes` table with proper schema
- Indexes for performance
- Row Level Security (RLS) policies
- Triggers for automatic timestamp updates

## Step 5: Test the Integration

1. Start your LDIS application
2. Go to the Archives page
3. Click "Sync QR Codes to Supabase" button
4. Check your Supabase dashboard > **Table Editor** > **qr_codes** to see synced data

## Step 6: Test Online Validation

1. Generate a document with QR code in LDIS
2. The QR code should now point to: `your-domain/validate-online/[document-id]`
3. Scan the QR code with a mobile device
4. You should see the online validation page

## Features

### Automatic Sync
- New QR codes are automatically synced to Supabase when documents are generated
- Manual sync available via "Sync QR Codes to Supabase" button in Archives

### Online Validation
- Public validation page at `/validate-online/[document-id]`
- No authentication required for validation
- Tracks scan counts and timestamps
- Mobile-friendly interface

### Security
- Row Level Security (RLS) enabled
- Public read access only for active QR codes
- Authenticated access required for modifications

## API Endpoints

- `POST /api/qr-codes/sync` - Sync existing QR codes to Supabase
- `GET /api/qr-codes/sync` - Get sync status
- `GET /api/validate-online/[id]` - Validate document online

## Troubleshooting

### Environment Variables Not Working
- Make sure `.env.local` is in the project root
- Restart your development server after adding environment variables
- Check that variable names start with `NEXT_PUBLIC_`

### Database Connection Issues
- Verify your Supabase URL and API key are correct
- Check that your Supabase project is active
- Ensure the SQL setup script ran successfully

### Sync Issues
- Check browser console for error messages
- Verify Supabase table exists and has correct schema
- Check RLS policies allow your operations

### Validation Not Working
- Ensure QR codes contain the correct validation URL format
- Check that the document exists in Supabase
- Verify the document is marked as active

## Support

If you encounter issues:
1. Check the browser console for error messages
2. Review Supabase logs in your dashboard
3. Ensure all environment variables are set correctly
4. Verify the database schema matches the setup script
