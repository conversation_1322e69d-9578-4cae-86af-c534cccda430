"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import {
  AlertCircle,
  ArrowLeft,
  Shield,
  Edit,
  CheckCircle,
  Printer,
  Bell,
  User,
  Settings,
  Image,
  FileText,
} from "lucide-react";
import { useNotifications } from "@/contexts/notification-context";
import type { Notification } from "@/contexts/notification-context";
import { DocumentPreview } from "@/components/document-preview";
import { getClientAdminId } from "@/lib/admin-utils";
import { getClientBaseURL } from "@/lib/network-utils";

export default function NotificationDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { notifications, markAsRead, isAdminMode, removeNotificationLocally } =
    useNotifications();
  const [notification, setNotification] = useState<Notification | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isHydrated, setIsHydrated] = useState(false);
  const [isApproved, setIsApproved] = useState(false);
  const [isArchiveView, setIsArchiveView] = useState(false);

  // Check if document is already approved based on status
  useEffect(() => {
    if (notification?.pdfData?.status === "approved") {
      setIsApproved(true);
    }
  }, [notification]);
  const [isProcessingApproval, setIsProcessingApproval] = useState(false);

  useEffect(() => {
    const checkAdminAndLoadData = async () => {
      // Set hydrated to true on client side
      setIsHydrated(true);
      setIsLoading(false);

      // Check if user has admin access
      if (!isAdminMode) {
        return;
      }

      const documentId = params.id as string;

      // Check if we're in archive view based on the current path
      const currentPath = window.location.pathname;
      const isArchive = currentPath.includes("/archives/");
      setIsArchiveView(isArchive);

      if (isArchive) {
        // Fetch archived document
        try {
          const response = await fetch(`/api/archives/${documentId}`);
          const data = await response.json();
          if (data.success) {
            // Convert archived document to notification-like format
            const archivedDoc = data.data;

            // Try to parse stored metadata for better reconstruction
            let storedMetadata = null;
            try {
              storedMetadata = archivedDoc.metadata
                ? JSON.parse(archivedDoc.metadata)
                : null;
            } catch (e) {
              console.warn("Failed to parse archived metadata:", e);
            }

            const convertedNotification: Notification = {
              id: archivedDoc.id,
              title: `Archived: ${archivedDoc.template_name}`,
              message: `Document for ${archivedDoc.applicant_name}`,
              type: "success" as const,
              isRead: true,
              createdAt: new Date(archivedDoc.approved_at),
              pdfUrl: `/api/archives/${archivedDoc.id}/pdf`, // Use archive PDF endpoint
              pdfData: storedMetadata?.pdfData || {
                templateId: storedMetadata?.templateId || archivedDoc.id,
                templateName: archivedDoc.template_name,
                placeholders: storedMetadata?.placeholders || [],
                userData: {
                  firstName: archivedDoc.first_name || "",
                  lastName: archivedDoc.last_name || "",
                  middleInitial: archivedDoc.middle_initial || "",
                  suffix: archivedDoc.suffix || "",
                },
                photoBase64: storedMetadata?.photoBase64,
                generatedAt: archivedDoc.approved_at,
                layoutSize: "A4" as const,
                status: "approved", // Ensure status is approved for archived documents
                approvedAt: archivedDoc.approved_at,
                approvedBy: archivedDoc.approved_by,
                // Store the approved HTML content if available
                approvedHtmlContent: storedMetadata?.approvedHtmlContent,
              },
            };
            setNotification(convertedNotification);
            setIsApproved(true);
          } else {
            console.error("Failed to fetch archived document:", data.error);
            // Set a placeholder notification to show error state
            setNotification(null);
          }
        } catch (error) {
          console.error("Error fetching archived document:", error);
          setNotification(null);
        }
      } else {
        // Original notification logic
        // Try to find in local state first
        let foundNotification = notifications.find((n) => n.id === documentId);

        // If not found locally, fetch from database
        if (!foundNotification) {
          try {
            const response = await fetch(`/api/notifications/${documentId}`);
            const data = await response.json();
            if (data.success) {
              foundNotification = {
                ...data.notification,
                createdAt: new Date(data.notification.createdAt),
              };
            }
          } catch (error) {
            console.error("Error fetching notification:", error);
          }
        }

        if (foundNotification) {
          setNotification(foundNotification);
          // Mark as read when viewing details
          if (!foundNotification.isRead) {
            markAsRead(foundNotification.id);
          }
        }
      }
    };

    checkAdminAndLoadData();
  }, [params.id, notifications, markAsRead, isAdminMode]);

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  const handleEdit = () => {
    if (notification?.pdfData?.templateId) {
      // Redirect to template form page with edit mode and notification ID
      const templateId = notification.pdfData.templateId;
      const queryParams = new URLSearchParams();
      queryParams.append("edit", "true");
      queryParams.append("notificationId", notification.id);

      // Navigate to the template form page - data will be fetched from notification API
      router.push(`/templates/form/${templateId}?${queryParams.toString()}`);
    }
  };

  const handleApproved = async () => {
    if (!notification?.pdfData) {
      toast.error("No PDF data available for approval");
      return;
    }

    setIsProcessingApproval(true);

    try {
      // Get current date
      const now = new Date();
      const day = now.getDate().toString();
      const month = now.toLocaleString("default", { month: "long" });
      const year = now.getFullYear().toString();

      // Fill up date fields in the user data
      const updatedUserData = { ...notification.pdfData.userData };

      // Find and fill date-related fields
      Object.keys(updatedUserData).forEach((key) => {
        const lowerKey = key.toLowerCase();
        if (lowerKey.includes("day") && !lowerKey.includes("birthday")) {
          updatedUserData[key] = day;
        } else if (lowerKey.includes("month") && !lowerKey.includes("birth")) {
          updatedUserData[key] = month;
        } else if (lowerKey.includes("year") && !lowerKey.includes("birth")) {
          updatedUserData[key] = year;
        }
      });

      // Create updated PDF data with approval status and filled dates
      const adminId = getClientAdminId();
      const updatedPdfData = {
        ...notification.pdfData,
        userData: updatedUserData,
        status: "approved", // Set status to approved
        approvedAt: now.toISOString(),
        approvedBy: adminId, // Track which admin approved this
      };

      // Generate QR code text using network IP for mobile accessibility
      let qrCodeText = `${getClientBaseURL()}/validate/${notification.id}`;
      try {
        const networkResponse = await fetch("/api/network-ip");
        const { baseURL, success } = await networkResponse.json();
        if (success) {
          qrCodeText = `${baseURL}/validate/${notification.id}`;
        }
      } catch (error) {
        console.warn("Failed to get network IP, using fallback:", error);
      }

      // Generate new PDF with QR code and filled dates
      const response = await fetch("/api/templates/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          templateId: notification.pdfData.templateId,
          data: updatedUserData,
          photoPath: null, // Will handle photo replacement manually
          addQRCode: true, // Flag to add QR code
          qrCodeText, // QR code content with network IP
          documentId: notification.id, // Pass notification ID for QR code storage
          documentType: "notification", // Specify document type
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to generate approved document");
      }

      const result = await response.json();

      // Store the approved HTML content for archiving
      const approvedHtmlContent = result.htmlContent;

      // Generate PDF with the updated HTML content (including QR code)
      const { jsPDF } = await import("jspdf");
      const html2canvas = (await import("html2canvas")).default;

      // Create an iframe to render the HTML content
      const iframe = document.createElement("iframe");
      iframe.style.position = "absolute";
      iframe.style.left = "-9999px";
      iframe.style.width = "794px";
      iframe.style.height = "1123px";
      document.body.appendChild(iframe);

      const iframeDoc =
        iframe.contentDocument || iframe.contentWindow?.document;
      if (!iframeDoc) {
        throw new Error("Could not access iframe document");
      }

      iframeDoc.open();
      iframeDoc.write(approvedHtmlContent);
      iframeDoc.close();

      // Handle existing photo replacement if needed
      if (notification.pdfData.photoBase64) {
        const photoSelectors = [
          'img[data-placeholder="applicant-photo"]',
          'img[src*="applicant-photo"]',
          'img[alt*="applicant"]',
          'img[alt*="photo"]',
          'img[src*="placeholder"]',
          'img[src*="default"]',
        ];

        const photoElements = iframeDoc.querySelectorAll(
          photoSelectors.join(", ")
        );
        photoElements.forEach((img) => {
          (img as HTMLImageElement).src = notification.pdfData!.photoBase64!;
        });
      }

      // Wait for content and QR code to render (reduced timeout)
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Convert to canvas and generate PDF
      const canvas = await html2canvas(iframeDoc.body, {
        scale: 1,
        useCORS: true,
        allowTaint: true,
        backgroundColor: "#ffffff",
        width: 794,
        height: 1123,
      });

      const imgData = canvas.toDataURL("image/jpeg", 0.8);
      const pdf = new jsPDF(
        "p",
        "pt",
        result.layoutSize === "Letter" ? "letter" : "a4"
      );
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();

      pdf.addImage(imgData, "JPEG", 0, 0, pdfWidth, pdfHeight);

      // Create updated embedded data
      const embeddedData = {
        ...updatedPdfData,
        photoBase64: notification.pdfData.photoBase64,
        generatedAt: new Date().toISOString(),
        layoutSize: result.layoutSize,
      };

      // Add embedded data as invisible text
      pdf.setTextColor(255, 255, 255);
      pdf.setFontSize(1);
      pdf.text(
        `LDIS_DATA_BEGIN:${JSON.stringify(embeddedData)}:LDIS_DATA_END`,
        1,
        1
      );

      // Convert PDF to blob for download/print (but don't upload)
      const pdfBlob = pdf.output("blob");

      // Store the approved PDF blob for potential download
      // You can implement a download feature later if needed
      console.log("Approved PDF generated, size:", pdfBlob.size);

      // Update metadata with approval status instead of uploading PDF
      const metadataFileName =
        notification.pdfData?.originalFileName ||
        `${notification.pdfData?.userData?.firstName || "Document"}_${
          notification.pdfData?.templateName || "Template"
        }.pdf`;

      if (!metadataFileName) {
        throw new Error("No metadata file reference found");
      }

      // Metadata is now stored directly in database, no separate file update needed
      console.log("PDF data updated in memory:", updatedPdfData);

      // Archive the document to SQLite database
      try {
        const archiveResponse = await fetch(
          `/api/notifications/${notification.id}/approve`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              approvedBy: "admin", // You can make this dynamic based on current user
              updatedPdfData: updatedPdfData, // Send the updated PDF data with approved status
              approvedHtmlContent: approvedHtmlContent, // Send the approved HTML content with QR code
            }),
          }
        );

        const archiveResult = await archiveResponse.json();

        if (archiveResponse.ok && archiveResult.success) {
          console.log("Document successfully archived to database");

          // Remove notification from context immediately for real-time update
          // Use local removal since the notification is already deleted on the server
          removeNotificationLocally(notification.id);

          // Clean up iframe
          document.body.removeChild(iframe);

          setIsApproved(true);
          toast.success("Document approved and archived successfully!");

          // Since the notification is deleted after archiving, redirect to archives
          setTimeout(() => {
            router.push("/archives");
          }, 2000);
          return; // Exit early since we're redirecting
        } else {
          throw new Error(archiveResult.error || "Failed to archive document");
        }
      } catch (archiveError) {
        console.error("Archive error:", archiveError);
        toast.error("Failed to approve and archive document");
        return; // Exit early on archive failure
      }

      // This code should not be reached since we return early after successful archiving
      console.warn("Unexpected code path reached after archiving");
    } catch (error) {
      console.error("Error approving document:", error);
      toast.error("Failed to approve document");
    } finally {
      setIsProcessingApproval(false);
    }
  };

  const handlePrint = () => {
    // TODO: Implement print functionality
    console.log("Print button clicked for notification:", notification?.id);
  };

  // Show loading state while checking admin mode or during SSR
  if (isLoading || !isHydrated) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show access denied if not in admin mode
  if (!isAdminMode) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <div className="text-center py-12">
          <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Admin Access Required</h3>
          <p className="text-muted-foreground mb-6">
            You need to enable admin mode to view notifications.
          </p>
          <Button onClick={() => router.push("/settings")}>
            <Shield className="h-4 w-4 mr-2" />
            Go to Settings
          </Button>
        </div>
      </div>
    );
  }

  if (!notification) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {isArchiveView
                ? "Archived Document Not Found"
                : "Notification Not Found"}
            </h3>
            <p className="text-muted-foreground mb-6">
              {isArchiveView
                ? "The archived document you're looking for doesn't exist or may not have been properly archived."
                : "The notification you're looking for doesn't exist or has been removed."}
            </p>
            <Button
              onClick={() => router.push(isArchiveView ? "/archives" : "/")}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              {isArchiveView ? "Back to Archives" : "Go Back"}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-[calc(100vh-120px)]">
      {/* Document Preview Area */}
      <div className="flex-1 flex flex-col">
        {notification.pdfData ? (
          <DocumentPreview
            pdfData={notification.pdfData}
            className="flex-1"
            notificationId={notification.id}
          />
        ) : (
          <div className="flex-1 flex items-center justify-center bg-background">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                No Document Data Available
              </h3>
              <p className="text-muted-foreground">
                This notification doesn't have associated document data.
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Bottom Panel - Template Details */}
      <div className="bg-background border-t">
        {/* Header with Actions */}
        <div className="p-4 border-b">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold">Template Details</h2>
            {/* Action Buttons */}
            <div className="flex gap-2">
              {!isArchiveView && (
                <>
                  <Button
                    onClick={handleEdit}
                    disabled={!notification?.pdfData?.templateId}
                    variant="outline"
                    size="sm"
                  >
                    Edit
                  </Button>
                  <Button
                    onClick={handleApproved}
                    disabled={isProcessingApproval || isApproved}
                    variant="default"
                    size="sm"
                  >
                    {isProcessingApproval
                      ? "Processing..."
                      : isApproved
                      ? "Approved"
                      : "Approve"}
                  </Button>
                </>
              )}
              {/* Print Button - Show after approval */}
              {isApproved && (
                <Button onClick={handlePrint} variant="secondary" size="sm">
                  Print
                </Button>
              )}
              {isArchiveView && (
                <div className="text-sm text-muted-foreground bg-green-50 dark:bg-green-900/20 px-3 py-2 rounded-md border border-green-200 dark:border-green-800">
                  <CheckCircle className="h-4 w-4 inline mr-2 text-green-600 dark:text-green-400" />
                  This document has been approved and archived
                </div>
              )}
            </div>
          </div>
        </div>
        {/* Content Area */}
        <div className="p-4 max-h-64 overflow-y-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* Notification Information */}
            <div>
              <h3 className="font-medium text-sm mb-3">Notification</h3>
              <div className="space-y-2">
                <div>
                  <label className="text-xs text-muted-foreground">Title</label>
                  <p className="text-sm">{notification.title}</p>
                </div>
                <div>
                  <label className="text-xs text-muted-foreground">
                    Uploaded
                  </label>
                  <p className="text-sm">
                    {formatDate(notification.createdAt.toString())}
                  </p>
                </div>
              </div>
            </div>

            {/* Document Status */}
            {notification.pdfData && (
              <div>
                <h3 className="font-medium text-sm mb-3">Status</h3>
                <div className="space-y-2">
                  <div>
                    <label className="text-xs text-muted-foreground">
                      Status
                    </label>
                    <p className="text-sm">
                      {notification.pdfData.status === "approved"
                        ? "Approved"
                        : "Pending Review"}
                    </p>
                  </div>

                  {notification.pdfData.approvedAt && (
                    <div>
                      <label className="text-xs text-muted-foreground">
                        Approved At
                      </label>
                      <p className="text-sm">
                        {formatDate(notification.pdfData.approvedAt)}
                      </p>
                    </div>
                  )}

                  {notification.pdfData.approvedBy && (
                    <div>
                      <label className="text-xs text-muted-foreground">
                        Approved By
                      </label>
                      <p className="text-sm">
                        {notification.pdfData.approvedBy}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Template Information */}
            {notification.pdfData && (
              <div>
                <h3 className="font-medium text-sm mb-3">Template</h3>
                <div className="space-y-2">
                  <div>
                    <label className="text-xs text-muted-foreground">
                      Template Name
                    </label>
                    <p className="text-sm">
                      {notification.pdfData.templateName}
                    </p>
                  </div>
                  <div>
                    <label className="text-xs text-muted-foreground">
                      Layout Size
                    </label>
                    <p className="text-sm">{notification.pdfData.layoutSize}</p>
                  </div>
                  <div>
                    <label className="text-xs text-muted-foreground">
                      Generated At
                    </label>
                    <p className="text-sm">
                      {formatDate(notification.pdfData.generatedAt)}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* User Data */}
            {notification.pdfData && (
              <div>
                <h3 className="font-medium text-sm mb-3">User Data</h3>
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {/* User Photo */}
                  {notification.pdfData.photoBase64 && (
                    <div>
                      <label className="text-xs text-muted-foreground">
                        Photo
                      </label>
                      <div className="mt-1">
                        <img
                          src={notification.pdfData.photoBase64}
                          alt="User photo"
                          className="w-16 h-16 object-cover border rounded"
                        />
                      </div>
                    </div>
                  )}

                  {/* User Data Fields */}
                  {Object.entries(notification.pdfData.userData).map(
                    ([key, value]) => (
                      <div key={key}>
                        <label className="text-xs text-muted-foreground">
                          {key.replace(/([A-Z])/g, " $1").trim()}
                        </label>
                        <p className="text-sm">
                          {value || (
                            <span className="italic text-muted-foreground">
                              Empty
                            </span>
                          )}
                        </p>
                      </div>
                    )
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
