import { NextRequest, NextResponse } from 'next/server';
import { getDatabase, createQRCode } from '@/lib/database';
import { syncMultipleQRCodesToSupabase } from '@/lib/supabase';
import { getNetworkIP } from '@/lib/network-utils';

// POST /api/qr-codes/sync - Generate QR codes for validation documents and sync to Supabase
export async function POST(request: NextRequest) {
  try {
    const database = await getDatabase();

    // First, generate QR codes for validation documents that don't have them
    const validationDocsWithoutQR = database.prepare(`
      SELECT v.id, v.first_name, v.last_name
      FROM qr_validations v
      LEFT JOIN qr_codes q ON v.id = q.document_id AND q.document_type = 'validation'
      WHERE q.id IS NULL
    `).all();

    let generated = 0;
    const networkIP = getNetworkIP(request);

    // Generate QR codes for validation documents without them
    for (const doc of validationDocsWithoutQR) {
      try {
        const qrCodeText = `${networkIP}/validate-online/${doc.id}`;
        const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(qrCodeText)}`;

        await createQRCode({
          document_id: doc.id,
          document_type: 'validation',
          qr_code_url: qrCodeUrl,
          validation_url: qrCodeText,
          network_ip: networkIP,
          size_dimensions: '150x150',
          display_dimensions: '60x60',
          created_by: 'system-migration'
        });

        generated++;
        console.log(`Generated QR code for validation document: ${doc.first_name} ${doc.last_name} (${doc.id})`);
      } catch (error) {
        console.error(`Failed to generate QR code for document ${doc.id}:`, error);
      }
    }

    // Now get all QR codes from local database (including newly generated ones)
    const stmt = database.prepare('SELECT * FROM qr_codes WHERE is_active = 1');
    const qrCodes = stmt.all();

    if (qrCodes.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No QR codes found to sync',
        generated,
        synced: 0,
        failed: 0
      });
    }

    // Sync to Supabase
    const result = await syncMultipleQRCodesToSupabase(qrCodes);

    return NextResponse.json({
      success: true,
      message: `Generated ${generated} QR codes and synced ${result.success} to Supabase (${result.failed} failed)`,
      generated,
      synced: result.success,
      failed: result.failed,
      total: qrCodes.length
    });
  } catch (error) {
    console.error('Error syncing QR codes to Supabase:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to sync QR codes to Supabase',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET /api/qr-codes/sync - Get sync status
export async function GET() {
  try {
    const database = await getDatabase();
    
    // Count local QR codes
    const localCountStmt = database.prepare('SELECT COUNT(*) as count FROM qr_codes WHERE is_active = 1');
    const localCount = localCountStmt.get() as { count: number };

    return NextResponse.json({
      success: true,
      localQRCodes: localCount.count,
      message: `Found ${localCount.count} active QR codes in local database`
    });
  } catch (error) {
    console.error('Error getting sync status:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get sync status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
