'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, AlertCircle, Eye, Calendar, Hash, RefreshCw } from 'lucide-react';
import { format } from 'date-fns';

interface ValidationResult {
  success: boolean;
  isValid: boolean;
  document?: {
    id: string;
    type: 'notification' | 'archive';
    validatedAt: string;
    scanCount: number;
    generatedAt: string;
  };
  error?: string;
}

export default function OnlineValidationPage() {
  const params = useParams();
  const documentId = params.id as string;
  const [validation, setValidation] = useState<ValidationResult | null>(null);
  const [loading, setLoading] = useState(true);

  const validateDocument = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/validate-online/${documentId}`);
      const data = await response.json();
      setValidation(data);
    } catch (error) {
      console.error('Error validating document:', error);
      setValidation({
        success: false,
        isValid: false,
        error: 'Failed to validate document'
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (documentId) {
      validateDocument();
    }
  }, [documentId]);

  const getStatusIcon = () => {
    if (loading) return <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />;
    if (validation?.isValid) return <CheckCircle className="h-8 w-8 text-green-500" />;
    return <XCircle className="h-8 w-8 text-red-500" />;
  };

  const getStatusColor = () => {
    if (loading) return 'border-blue-200 bg-blue-50';
    if (validation?.isValid) return 'border-green-200 bg-green-50';
    return 'border-red-200 bg-red-50';
  };

  const getStatusTitle = () => {
    if (loading) return 'Validating Document...';
    if (validation?.isValid) return 'Document Verified ✓';
    return 'Document Not Valid ✗';
  };

  const getStatusDescription = () => {
    if (loading) return 'Please wait while we verify this document online...';
    if (validation?.isValid) return 'This document has been verified as authentic and approved.';
    return validation?.error || 'This document could not be verified or may not be authentic.';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Document Verification
          </h1>
          <p className="text-gray-600">
            Online verification system
          </p>
        </div>

        {/* Main Validation Card */}
        <Card className={`${getStatusColor()} border-2`}>
          <CardHeader className="text-center pb-4">
            <div className="flex justify-center mb-4">
              {getStatusIcon()}
            </div>
            <CardTitle className="text-xl">
              {getStatusTitle()}
            </CardTitle>
            <CardDescription className="text-base">
              {getStatusDescription()}
            </CardDescription>
          </CardHeader>

          {validation?.isValid && validation.document && (
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Hash className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="font-medium">Document ID</p>
                    <p className="text-gray-600 font-mono text-xs">
                      {validation.document.id.substring(0, 8)}...
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Badge variant={validation.document.type === 'notification' ? 'default' : 'secondary'}>
                    {validation.document.type === 'notification' ? 'Notification' : 'Archive'}
                  </Badge>
                </div>

                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="font-medium">Generated</p>
                    <p className="text-gray-600 text-xs">
                      {format(new Date(validation.document.generatedAt), 'MMM dd, yyyy')}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Eye className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="font-medium">Scans</p>
                    <p className="text-gray-600 text-xs">
                      {validation.document.scanCount} times
                    </p>
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t">
                <div className="flex items-center gap-2 text-xs text-gray-500">
                  <AlertCircle className="h-3 w-3" />
                  <span>Verified at {format(new Date(validation.document.validatedAt), 'MMM dd, yyyy HH:mm')}</span>
                </div>
              </div>
            </CardContent>
          )}
        </Card>

        {/* Action Buttons */}
        <div className="flex gap-3">
          <Button 
            onClick={validateDocument} 
            disabled={loading}
            variant="outline" 
            className="flex-1"
          >
            {loading ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Verify Again
          </Button>
        </div>

        {/* Footer */}
        <div className="text-center text-xs text-gray-500">
          <p>LDIS - Local Document Issuance System</p>
          <p>Online Document Verification</p>
        </div>
      </div>
    </div>
  );
}
