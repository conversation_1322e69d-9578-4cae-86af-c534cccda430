import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';

// GET /api/qr-codes - Get all QR codes with optional filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const documentType = searchParams.get('type');
    const isActive = searchParams.get('active');
    const limit = parseInt(searchParams.get('limit') || '100');
    const offset = parseInt(searchParams.get('offset') || '0');

    const database = await getDatabase();
    
    let query = 'SELECT * FROM qr_codes';
    const conditions: string[] = [];
    const values: any[] = [];

    if (documentType && documentType !== 'all') {
      conditions.push('document_type = ?');
      values.push(documentType);
    }

    if (isActive && isActive !== 'all') {
      conditions.push('is_active = ?');
      values.push(isActive === 'active' ? 1 : 0);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY generated_at DESC LIMIT ? OFFSET ?';
    values.push(limit, offset);

    const stmt = database.prepare(query);
    const qrCodes = stmt.all(...values);

    // Get total count for pagination
    let countQuery = 'SELECT COUNT(*) as total FROM qr_codes';
    const countValues: any[] = [];

    if (conditions.length > 0) {
      countQuery += ' WHERE ' + conditions.join(' AND ');
      // Add the same condition values (excluding limit and offset)
      for (let i = 0; i < values.length - 2; i++) {
        countValues.push(values[i]);
      }
    }

    const countStmt = database.prepare(countQuery);
    const countResult = countStmt.get(...countValues) as { total: number };

    return NextResponse.json({
      success: true,
      qrCodes,
      pagination: {
        total: countResult.total,
        limit,
        offset,
        hasMore: offset + limit < countResult.total
      }
    });
  } catch (error) {
    console.error('Error fetching QR codes:', error);
    return NextResponse.json(
      { error: 'Failed to fetch QR codes' },
      { status: 500 }
    );
  }
}
