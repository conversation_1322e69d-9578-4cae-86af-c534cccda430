import { NextRequest, NextResponse } from 'next/server';
import { getArchivedDocument, deleteArchivedDocument } from '@/lib/database';
import { NotificationModel } from '@/lib/models/notification';
import { getUserFromSession } from '@/lib/auth-utils';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Get current user from session
    const currentUser = await getUserFromSession(request);
    if (!currentUser) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get the archived document
    const archivedDoc = await getArchivedDocument(id);
    
    if (!archivedDoc) {
      return NextResponse.json(
        { success: false, error: 'Archived document not found' },
        { status: 404 }
      );
    }

    // Parse the stored metadata to get the original notification data
    let originalData = null;
    if (archivedDoc.metadata) {
      try {
        originalData = JSON.parse(archivedDoc.metadata);
      } catch (parseError) {
        console.error('Error parsing archived document metadata:', parseError);
      }
    }

    // Create a new notification from the archived document
    const notificationData = {
      id: archivedDoc.id, // Use the same ID
      title: originalData?.title || `Restored: ${archivedDoc.template_name}`,
      message: originalData?.message || `Document for ${archivedDoc.applicant_name}`,
      type: 'info' as const,
      isRead: false,
      createdAt: new Date().toISOString(), // New creation time for restored document
      userId: originalData?.userId || currentUser.id, // Use original user ID or current user
      pdfData: originalData?.pdfData || {
        templateId: originalData?.templateId || archivedDoc.id,
        templateName: archivedDoc.template_name,
        userData: {
          firstName: archivedDoc.first_name || '',
          lastName: archivedDoc.last_name || '',
          middleInitial: archivedDoc.middle_initial || '',
          suffix: archivedDoc.suffix || '',
        },
        photoBase64: originalData?.pdfData?.photoBase64,
        generatedAt: new Date().toISOString(),
        layoutSize: 'A4' as const,
        status: null, // Reset to pending status
        approvedAt: null,
        approvedBy: null,
      }
    };

    // Create the notification in the database
    const createdNotification = await NotificationModel.create(notificationData);

    if (!createdNotification) {
      return NextResponse.json(
        { success: false, error: 'Failed to create restored notification' },
        { status: 500 }
      );
    }

    // Delete the archived document
    const deleteSuccess = await deleteArchivedDocument(id);

    if (!deleteSuccess) {
      // If deletion fails, we should clean up the created notification
      await NotificationModel.delete(archivedDoc.id);
      return NextResponse.json(
        { success: false, error: 'Failed to remove document from archives' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Document restored successfully',
      data: {
        notificationId: createdNotification.id,
        restoredAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Restore document error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to restore document' },
      { status: 500 }
    );
  }
}
