-- Create QR codes table in Supabase
-- Run this SQL in your Supabase SQL Editor

CREATE TABLE IF NOT EXISTS qr_codes (
  id TEXT PRIMARY KEY,
  document_id TEXT NOT NULL,
  document_type TEXT NOT NULL CHECK (document_type IN ('notification', 'archive', 'validation')),
  qr_code_url TEXT NOT NULL,
  validation_url TEXT NOT NULL,
  network_ip TEXT,
  size_dimensions TEXT DEFAULT '150x150',
  display_dimensions TEXT DEFAULT '60x60',
  generated_at TIMESTAMPTZ NOT NULL,
  expires_at TIMESTAMPTZ,
  is_active BOOLEAN DEFAULT TRUE,
  scan_count INTEGER DEFAULT 0,
  last_scanned_at TIMESTAMPTZ,
  created_by TEXT,
  synced_at TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_qr_codes_document_id ON qr_codes(document_id);
CREATE INDEX IF NOT EXISTS idx_qr_codes_document_type ON qr_codes(document_type);
CREATE INDEX IF NOT EXISTS idx_qr_codes_is_active ON qr_codes(is_active);
CREATE INDEX IF NOT EXISTS idx_qr_codes_generated_at ON qr_codes(generated_at);

-- Enable Row Level Security (RLS)
ALTER TABLE qr_codes ENABLE ROW LEVEL SECURITY;

-- Create policy to allow public read access for document validation
CREATE POLICY "Allow public read access for validation" ON qr_codes
  FOR SELECT USING (is_active = true);

-- Create policy to allow authenticated users to insert/update
CREATE POLICY "Allow authenticated users to manage QR codes" ON qr_codes
  FOR ALL USING (auth.role() = 'authenticated');

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_qr_codes_updated_at 
  BEFORE UPDATE ON qr_codes 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- Optional: Create a view for public document validation (without sensitive data)
CREATE OR REPLACE VIEW public_qr_validation AS
SELECT 
  id,
  document_id,
  document_type,
  is_active,
  generated_at,
  expires_at,
  scan_count
FROM qr_codes
WHERE is_active = true;

-- Grant access to the view
GRANT SELECT ON public_qr_validation TO anon, authenticated;
