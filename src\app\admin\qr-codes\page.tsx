"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { QrCode, Eye, Hash, Activity } from "lucide-react";
import { toast } from "sonner";

interface QRCodeRecord {
  id: string;
  document_id: string;
  document_type: "notification" | "archive";
  qr_code_url: string;
  validation_url: string;
  network_ip?: string;
  size_dimensions: string;
  display_dimensions: string;
  generated_at: string;
  expires_at?: string;
  is_active: boolean;
  scan_count: number;
  last_scanned_at?: string;
  created_by?: string;
}

export default function QRCodesPage() {
  const [qrCodes, setQrCodes] = useState<QRCodeRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<
    "all" | "notification" | "archive"
  >("all");
  const [filterActive, setFilterActive] = useState<
    "all" | "active" | "inactive"
  >("all");

  useEffect(() => {
    fetchQRCodes();
  }, [filterType, filterActive]);

  const fetchQRCodes = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        type: filterType,
        active: filterActive,
        limit: "100",
      });

      const response = await fetch(`/api/qr-codes?${params}`);
      if (!response.ok) {
        throw new Error("Failed to fetch QR codes");
      }

      const data = await response.json();
      if (data.success) {
        setQrCodes(data.qrCodes || []);
        if (data.qrCodes.length === 0) {
          toast.info(
            "QR codes database table created successfully! QR codes will appear here as documents are approved."
          );
        }
      } else {
        throw new Error(data.error || "Failed to fetch QR codes");
      }
    } catch (error) {
      console.error("Error fetching QR codes:", error);
      toast.error("Failed to fetch QR codes");
      setQrCodes([]);
    } finally {
      setLoading(false);
    }
  };

  const filteredQRCodes = qrCodes.filter((qr) => {
    const matchesSearch =
      qr.document_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      qr.validation_url.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === "all" || qr.document_type === filterType;
    const matchesActive =
      filterActive === "all" ||
      (filterActive === "active" && qr.is_active) ||
      (filterActive === "inactive" && !qr.is_active);

    return matchesSearch && matchesType && matchesActive;
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getTypeColor = (type: string) => {
    return type === "notification"
      ? "bg-blue-100 text-blue-800"
      : "bg-green-100 text-green-800";
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            QR Code Management
          </h1>
          <p className="text-muted-foreground">
            Monitor and manage QR codes for approved documents
          </p>
        </div>
        <Button onClick={fetchQRCodes} disabled={loading}>
          <Activity className="mr-2 h-4 w-4" />
          Refresh
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total QR Codes
            </CardTitle>
            <QrCode className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{qrCodes.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active QR Codes
            </CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {qrCodes.filter((qr) => qr.is_active).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Scans</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {qrCodes.reduce((sum, qr) => sum + qr.scan_count, 0)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Notifications</CardTitle>
            <Hash className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {
                qrCodes.filter((qr) => qr.document_type === "notification")
                  .length
              }
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
          <CardDescription>
            Filter QR codes by search term, type, and status
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search by document ID or validation URL..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select
              value={filterType}
              onValueChange={(value: "all" | "notification" | "archive") =>
                setFilterType(value)
              }
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Document Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="notification">Notifications</SelectItem>
                <SelectItem value="archive">Archives</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={filterActive}
              onValueChange={(value: "all" | "active" | "inactive") =>
                setFilterActive(value)
              }
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* QR Codes Table */}
      <Card>
        <CardHeader>
          <CardTitle>QR Codes ({filteredQRCodes.length})</CardTitle>
          <CardDescription>
            List of all QR codes generated for approved documents
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">Loading QR codes...</div>
          ) : filteredQRCodes.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <QrCode className="mx-auto h-12 w-12 mb-4" />
              <p>No QR codes found</p>
              <p className="text-sm">
                QR codes will appear here when documents are approved with QR
                codes enabled
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Document ID</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Scans</TableHead>
                  <TableHead>Generated</TableHead>
                  <TableHead>Last Scanned</TableHead>
                  <TableHead>Network IP</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredQRCodes.map((qr) => (
                  <TableRow key={qr.id}>
                    <TableCell className="font-mono text-sm">
                      {qr.document_id.substring(0, 8)}...
                    </TableCell>
                    <TableCell>
                      <Badge className={getTypeColor(qr.document_type)}>
                        {qr.document_type}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={qr.is_active ? "default" : "secondary"}>
                        {qr.is_active ? "Active" : "Inactive"}
                      </Badge>
                    </TableCell>
                    <TableCell>{qr.scan_count}</TableCell>
                    <TableCell>{formatDate(qr.generated_at)}</TableCell>
                    <TableCell>
                      {qr.last_scanned_at
                        ? formatDate(qr.last_scanned_at)
                        : "Never"}
                    </TableCell>
                    <TableCell className="font-mono text-sm">
                      {qr.network_ip || "N/A"}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
