"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Search, QrCode } from "lucide-react";

export default function ValidateHomePage() {
  const [documentId, setDocumentId] = useState("");
  const router = useRouter();

  const handleValidate = () => {
    if (documentId.trim()) {
      router.push(`/validate/${documentId.trim()}`);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleValidate();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <Card className="w-full max-w-md shadow-xl">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="rounded-full bg-blue-100 dark:bg-blue-900/20 p-3">
              <QrCode className="h-12 w-12 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold">Document Validation</CardTitle>
          <p className="text-muted-foreground mt-2">
            Enter a document ID to verify its authenticity and approval status.
          </p>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="documentId">Document ID</Label>
            <Input
              id="documentId"
              type="text"
              placeholder="Enter document ID..."
              value={documentId}
              onChange={(e) => setDocumentId(e.target.value)}
              onKeyPress={handleKeyPress}
            />
          </div>
          
          <Button 
            onClick={handleValidate} 
            className="w-full"
            disabled={!documentId.trim()}
          >
            <Search className="h-4 w-4 mr-2" />
            Validate Document
          </Button>
          
          <div className="text-center text-sm text-muted-foreground">
            <p>Or scan the QR code on an approved document</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
