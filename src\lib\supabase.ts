import { createClient } from '@supabase/supabase-js';

// Supabase configuration with fallbacks for build time
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key';

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Helper function to check if Supabase is properly configured
export const isSupabaseConfigured = () => {
  return !!(process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
};

// QR Code interface for Supabase
export interface SupabaseQRCode {
  id: string;
  document_id: string;
  document_type: 'notification' | 'archive' | 'validation';
  qr_code_url: string;
  validation_url: string;
  network_ip?: string;
  size_dimensions: string;
  display_dimensions: string;
  generated_at: string;
  expires_at?: string;
  is_active: boolean;
  scan_count: number;
  last_scanned_at?: string;
  created_by?: string;
  synced_at: string;
}

// Input QR Code interface
export interface QRCodeInput {
  id: string;
  document_id: string;
  document_type: 'notification' | 'archive' | 'validation';
  qr_code_url: string;
  validation_url: string;
  network_ip?: string;
  size_dimensions?: string;
  display_dimensions?: string;
  generated_at: string;
  expires_at?: string;
  is_active: boolean;
  scan_count?: number;
  last_scanned_at?: string;
  created_by?: string;
}

// Sync QR code to Supabase
export const syncQRCodeToSupabase = async (qrCode: QRCodeInput): Promise<boolean> => {
  if (!isSupabaseConfigured()) {
    console.warn('Supabase not configured, skipping QR code sync');
    return false;
  }

  try {
    const supabaseQRCode: SupabaseQRCode = {
      id: qrCode.id,
      document_id: qrCode.document_id,
      document_type: qrCode.document_type,
      qr_code_url: qrCode.qr_code_url,
      validation_url: qrCode.validation_url,
      network_ip: qrCode.network_ip,
      size_dimensions: qrCode.size_dimensions || '150x150',
      display_dimensions: qrCode.display_dimensions || '60x60',
      generated_at: qrCode.generated_at,
      expires_at: qrCode.expires_at,
      is_active: qrCode.is_active,
      scan_count: qrCode.scan_count || 0,
      last_scanned_at: qrCode.last_scanned_at,
      created_by: qrCode.created_by,
      synced_at: new Date().toISOString()
    };

    const { error } = await supabase
      .from('qr_codes')
      .upsert(supabaseQRCode, { 
        onConflict: 'id',
        ignoreDuplicates: false 
      });

    if (error) {
      console.error('Error syncing QR code to Supabase:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error syncing QR code to Supabase:', error);
    return false;
  }
};

// Sync multiple QR codes to Supabase
export const syncMultipleQRCodesToSupabase = async (qrCodes: QRCodeInput[]): Promise<{ success: number; failed: number }> => {
  let success = 0;
  let failed = 0;

  for (const qrCode of qrCodes) {
    const result = await syncQRCodeToSupabase(qrCode);
    if (result) {
      success++;
    } else {
      failed++;
    }
  }

  return { success, failed };
};

// Get QR code from Supabase by document ID
export const getQRCodeFromSupabase = async (documentId: string): Promise<SupabaseQRCode | null> => {
  if (!isSupabaseConfigured()) {
    console.warn('Supabase not configured, cannot get QR code');
    return null;
  }

  try {
    const { data, error } = await supabase
      .from('qr_codes')
      .select('*')
      .eq('document_id', documentId)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching QR code from Supabase:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error fetching QR code from Supabase:', error);
    return null;
  }
};

// Verify document online via Supabase
export const verifyDocumentOnline = async (documentId: string): Promise<{
  isValid: boolean;
  document?: SupabaseQRCode;
  error?: string;
}> => {
  if (!isSupabaseConfigured()) {
    return {
      isValid: false,
      error: 'Online verification not available - Supabase not configured'
    };
  }

  try {
    const qrCode = await getQRCodeFromSupabase(documentId);
    
    if (!qrCode) {
      return {
        isValid: false,
        error: 'Document not found or not verified online'
      };
    }

    // Check if document is still active and not expired
    if (!qrCode.is_active) {
      return {
        isValid: false,
        error: 'Document verification has been deactivated'
      };
    }

    if (qrCode.expires_at && new Date(qrCode.expires_at) < new Date()) {
      return {
        isValid: false,
        error: 'Document verification has expired'
      };
    }

    // Increment scan count
    await supabase
      .from('qr_codes')
      .update({ 
        scan_count: qrCode.scan_count + 1,
        last_scanned_at: new Date().toISOString()
      })
      .eq('id', qrCode.id);

    return {
      isValid: true,
      document: qrCode
    };
  } catch (error) {
    console.error('Error verifying document online:', error);
    return {
      isValid: false,
      error: 'Error verifying document'
    };
  }
};
