import { getDatabase } from '../database';

// Generate UUID function for server-side use
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

export interface PDFData {
  templateId: string;
  templateName: string;
  placeholders: string[];
  userData: Record<string, string>;
  photoBase64?: string;
  generatedAt: string;
  layoutSize: "A4" | "Letter";
  status?: string | null; // null = pending, "approved" = approved, etc.
  approvedAt?: string;
  approvedBy?: string;
  originalFileName?: string;
  uploadedAt?: string;
  fileSize?: number;
  extractedAt?: string;
  updatedAt?: string;

}

export interface DatabaseNotification {
  id: string;
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
  pdfFileName?: string;
  pdfData?: PDFData;
  userId?: number; // Track which user/admin performed actions
}

export interface CreateNotificationData {
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  pdfFileName?: string;
  pdfData?: PDFData;
  userId?: number; // Track which user/admin created the notification
}

// Notification database operations
export class NotificationModel {
  static async create(notificationData: CreateNotificationData): Promise<DatabaseNotification> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    const id = generateUUID();
    
    const now = new Date().toISOString();

    const stmt = db.prepare(`
      INSERT INTO notifications (
        id, title, message, type, pdf_filename, pdf_data, user_id, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      id,
      notificationData.title,
      notificationData.message,
      notificationData.type,
      notificationData.pdfFileName || null,
      notificationData.pdfData ? JSON.stringify(notificationData.pdfData) : null,
      notificationData.userId || null,
      now,
      now
    );
    
    return await this.findById(id) as DatabaseNotification;
  }

  static async findById(id: string, userId?: number): Promise<DatabaseNotification | null> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    let stmt;
    let row;

    if (userId) {
      stmt = db.prepare('SELECT * FROM notifications WHERE id = ? AND (user_id = ? OR user_id IS NULL)');
      row = stmt.get(id, userId) as any;
    } else {
      stmt = db.prepare('SELECT * FROM notifications WHERE id = ?');
      row = stmt.get(id) as any;
    }

    if (!row) return null;

    return this.mapRowToNotification(row);
  }

  static async findAll(limit?: number, userId?: number): Promise<DatabaseNotification[]> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    let query;
    let stmt;
    let rows;

    if (userId) {
      query = limit
        ? 'SELECT * FROM notifications WHERE (user_id = ? OR user_id IS NULL) ORDER BY created_at DESC LIMIT ?'
        : 'SELECT * FROM notifications WHERE (user_id = ? OR user_id IS NULL) ORDER BY created_at DESC';
      stmt = db.prepare(query);
      rows = limit ? stmt.all(userId, limit) : stmt.all(userId);
    } else {
      query = limit
        ? 'SELECT * FROM notifications ORDER BY created_at DESC LIMIT ?'
        : 'SELECT * FROM notifications ORDER BY created_at DESC';
      stmt = db.prepare(query);
      rows = limit ? stmt.all(limit) : stmt.all();
    }

    return (rows as any[]).map(row => this.mapRowToNotification(row));
  }

  // Removed findByUserId method as userId is no longer used

  static async markAsRead(id: string): Promise<boolean> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    const stmt = db.prepare(`
      UPDATE notifications
      SET is_read = TRUE, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);

    const result = stmt.run(id);
    return result.changes > 0;
  }

  static async update(id: string, updateData: Partial<CreateNotificationData>): Promise<boolean> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    const now = new Date().toISOString();

    // Build dynamic update query based on provided fields
    const updateFields: string[] = [];
    const values: any[] = [];

    if (updateData.title !== undefined) {
      updateFields.push('title = ?');
      values.push(updateData.title);
    }

    if (updateData.message !== undefined) {
      updateFields.push('message = ?');
      values.push(updateData.message);
    }

    if (updateData.type !== undefined) {
      updateFields.push('type = ?');
      values.push(updateData.type);
    }

    if (updateData.pdfFileName !== undefined) {
      updateFields.push('pdf_filename = ?');
      values.push(updateData.pdfFileName);
    }

    if (updateData.pdfData !== undefined) {
      updateFields.push('pdf_data = ?');
      values.push(updateData.pdfData ? JSON.stringify(updateData.pdfData) : null);
    }

    if (updateData.userId !== undefined) {
      updateFields.push('user_id = ?');
      values.push(updateData.userId);
    }

    // Always update the updated_at timestamp
    updateFields.push('updated_at = ?');
    values.push(now);

    // Add the id for the WHERE clause
    values.push(id);

    const query = `UPDATE notifications SET ${updateFields.join(', ')} WHERE id = ?`;
    const stmt = db.prepare(query);
    const result = stmt.run(...values);

    return result.changes > 0;
  }

  static async markAllAsRead(): Promise<number> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    const stmt = db.prepare(`
      UPDATE notifications
      SET is_read = TRUE, updated_at = CURRENT_TIMESTAMP
      WHERE is_read = FALSE
    `);
    const result = stmt.run();

    return result.changes;
  }

  static async delete(id: string): Promise<boolean> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    const stmt = db.prepare('DELETE FROM notifications WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  static async deleteAll(): Promise<number> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    const stmt = db.prepare('DELETE FROM notifications');
    const result = stmt.run();

    return result.changes;
  }

  static async getUnreadCount(userId?: number): Promise<number> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    let stmt;
    let result;
    
    if (userId) {
      stmt = db.prepare(`
        SELECT COUNT(*) as count 
        FROM notifications 
        WHERE (user_id = ? OR user_id IS NULL) AND is_read = FALSE
      `);
      result = stmt.get(userId) as any;
    } else {
      stmt = db.prepare('SELECT COUNT(*) as count FROM notifications WHERE is_read = FALSE');
      result = stmt.get() as any;
    }
    
    return result.count;
  }

  private static mapRowToNotification(row: any): DatabaseNotification {
    return {
      id: row.id,
      title: row.title,
      message: row.message,
      type: row.type,
      isRead: Boolean(row.is_read),
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      pdfFileName: row.pdf_filename,
      pdfData: row.pdf_data ? JSON.parse(row.pdf_data) : undefined,
      userId: row.user_id
    };
  }
}
