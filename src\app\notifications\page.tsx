"use client";

import { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Bell,
  BellOff,
  Search,
  Filter,
  Trash2,
  CheckCircle,
  Circle,
  FileText,
  Calendar,
  User,
  Eye,
  EyeOff,
  MoreVertical,
  Download,
} from "lucide-react";
import { toast } from "sonner";
import { useNotifications } from "@/contexts/notification-context";
import type { Notification } from "@/contexts/notification-context";
import { formatDistanceToNow } from "date-fns";

type SortOption = "newest" | "oldest" | "template" | "status";
type FilterOption = "all" | "read" | "unread";

export default function NotificationsPage() {
  const router = useRouter();
  const {
    notifications,
    isAdminMode,
    markAsRead,
    markAllAsRead,
    removeNotification,
    isLoading,
  } = useNotifications();

  // State management
  const [selectedNotifications, setSelectedNotifications] = useState<
    Set<string>
  >(new Set());
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<SortOption>("newest");
  const [filterBy, setFilterBy] = useState<FilterOption>("all");
  const [isDeleting, setIsDeleting] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Extract unique templates from notifications
  const uniqueTemplates = useMemo(() => {
    const templates = new Set<string>();
    notifications.forEach((notification) => {
      if (notification.pdfData?.templateName) {
        templates.add(notification.pdfData.templateName);
      }
    });
    return Array.from(templates).sort();
  }, [notifications]);

  // Filter and sort notifications
  const filteredAndSortedNotifications = useMemo(() => {
    let filtered = notifications;

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(
        (notification) =>
          notification.title
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          notification.message
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          notification.pdfData?.templateName
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    if (filterBy !== "all") {
      filtered = filtered.filter((notification) => {
        if (filterBy === "read") return notification.isRead;
        if (filterBy === "unread") return !notification.isRead;
        return true;
      });
    }

    // Apply sorting
    const sorted = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case "newest":
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        case "oldest":
          return (
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          );
        case "template":
          const templateA = a.pdfData?.templateName || "";
          const templateB = b.pdfData?.templateName || "";
          return templateA.localeCompare(templateB);
        case "status":
          if (a.isRead === b.isRead) {
            return (
              new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
            );
          }
          return a.isRead ? 1 : -1;
        default:
          return 0;
      }
    });

    return sorted;
  }, [notifications, searchQuery, filterBy, sortBy]);

  // Selection handlers
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedNotifications(
        new Set(filteredAndSortedNotifications.map((n) => n.id))
      );
    } else {
      setSelectedNotifications(new Set());
    }
  };

  const handleSelectNotification = (id: string, checked: boolean) => {
    const newSelected = new Set(selectedNotifications);
    if (checked) {
      newSelected.add(id);
    } else {
      newSelected.delete(id);
    }
    setSelectedNotifications(newSelected);
  };

  // Bulk operations
  const handleBulkDelete = async () => {
    if (selectedNotifications.size === 0) {
      toast.error("Please select notifications to delete");
      return;
    }

    if (
      !confirm(
        `Are you sure you want to delete ${selectedNotifications.size} notification(s)?`
      )
    ) {
      return;
    }

    setIsDeleting(true);
    try {
      const response = await fetch("/api/notifications/bulk", {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          notificationIds: Array.from(selectedNotifications),
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to delete notifications");
      }

      const result = await response.json();

      // Remove from local state
      selectedNotifications.forEach((id) => removeNotification(id));
      setSelectedNotifications(new Set());

      toast.success(
        result.message ||
          `${selectedNotifications.size} notification(s) deleted successfully`
      );
    } catch (error) {
      console.error("Error deleting notifications:", error);
      toast.error("Failed to delete notifications");
    } finally {
      setIsDeleting(false);
    }
  };

  const handleBulkMarkAsRead = async () => {
    if (selectedNotifications.size === 0) {
      toast.error("Please select notifications to mark as read");
      return;
    }

    try {
      const response = await fetch("/api/notifications/bulk", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "markSelectedAsRead",
          notificationIds: Array.from(selectedNotifications),
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to mark notifications as read");
      }

      const result = await response.json();

      // Update local state
      selectedNotifications.forEach((id) => markAsRead(id));
      setSelectedNotifications(new Set());

      toast.success(
        result.message ||
          `${selectedNotifications.size} notification(s) marked as read`
      );
    } catch (error) {
      console.error("Error marking notifications as read:", error);
      toast.error("Failed to mark notifications as read");
    }
  };

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.isRead) {
      markAsRead(notification.id);
    }
    router.push(`/notifications/${notification.id}`);
  };

  // Don't render during SSR
  if (!isHydrated) {
    return null;
  }

  // Check admin access
  if (!isAdminMode) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <BellOff className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Access Restricted</h3>
          <p className="text-muted-foreground">
            You need admin access to view notifications.
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading notifications...</p>
          </div>
        </div>
      </div>
    );
  }

  const unreadCount = notifications.filter((n) => !n.isRead).length;
  const selectedCount = selectedNotifications.size;
  const isAllSelected =
    selectedCount > 0 &&
    selectedCount === filteredAndSortedNotifications.length;
  const isPartiallySelected =
    selectedCount > 0 && selectedCount < filteredAndSortedNotifications.length;

  return (
    <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8 max-w-6xl">
      {/* Header */}
      <div className="mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-semibold text-foreground mb-2">
              Notifications
            </h1>
            <p className="text-muted-foreground text-sm sm:text-base">
              Manage and view all document notifications
              {unreadCount > 0 && (
                <Badge variant="destructive" className="ml-2">
                  {unreadCount} unread
                </Badge>
              )}
            </p>
          </div>

          {notifications.length > 0 && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={markAllAsRead}
                disabled={unreadCount === 0}
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Mark All Read
              </Button>
            </div>
          )}
        </div>
      </div>

      {notifications.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Bell className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No notifications yet</h3>
            <p className="text-muted-foreground text-center">
              Document notifications will appear here when they are generated.
            </p>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Filters and Search */}
          <Card className="mb-6">
            <CardHeader className="pb-4">
              <div className="flex flex-col sm:flex-row gap-4">
                {/* Search */}
                <div className="flex-1">
                  <Label htmlFor="search" className="sr-only">
                    Search notifications
                  </Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="search"
                      placeholder="Search notifications..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                {/* Sort */}
                <div className="w-full sm:w-48">
                  <Label htmlFor="sort" className="sr-only">
                    Sort by
                  </Label>
                  <Select
                    value={sortBy}
                    onValueChange={(value: SortOption) => setSortBy(value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="newest">Newest First</SelectItem>
                      <SelectItem value="oldest">Oldest First</SelectItem>
                      <SelectItem value="template">Template Name</SelectItem>
                      <SelectItem value="status">Read Status</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Filter */}
                <div className="w-full sm:w-32">
                  <Label htmlFor="filter" className="sr-only">
                    Filter by status
                  </Label>
                  <Select
                    value={filterBy}
                    onValueChange={(value: FilterOption) => setFilterBy(value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Filter" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="unread">Unread</SelectItem>
                      <SelectItem value="read">Read</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Bulk Actions */}
          {selectedCount > 0 && (
            <Card className="mb-4 border-primary/20 bg-primary/5">
              <CardContent className="py-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">{selectedCount} selected</Badge>
                    <span className="text-sm text-muted-foreground">
                      of {filteredAndSortedNotifications.length} notifications
                    </span>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleBulkMarkAsRead}
                      disabled={isDeleting}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      Mark as Read
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={handleBulkDelete}
                      disabled={isDeleting}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      {isDeleting ? "Deleting..." : "Delete"}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Select All */}
          {filteredAndSortedNotifications.length > 0 && (
            <div className="flex items-center gap-2 mb-4 px-1">
              <Checkbox
                checked={isAllSelected}
                onCheckedChange={handleSelectAll}
                className={
                  isPartiallySelected
                    ? "data-[state=checked]:bg-primary/50"
                    : ""
                }
              />
              <Label className="text-sm text-muted-foreground cursor-pointer">
                Select all {filteredAndSortedNotifications.length} notifications
              </Label>
            </div>
          )}

          {/* Notifications List */}
          <div className="space-y-3">
            {filteredAndSortedNotifications.map((notification) => (
              <NotificationCard
                key={notification.id}
                notification={notification}
                isSelected={selectedNotifications.has(notification.id)}
                onSelect={(checked) =>
                  handleSelectNotification(notification.id, checked)
                }
                onClick={() => handleNotificationClick(notification)}
              />
            ))}
          </div>

          {filteredAndSortedNotifications.length === 0 && (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Filter className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  No notifications found
                </h3>
                <p className="text-muted-foreground text-center">
                  Try adjusting your search or filter criteria.
                </p>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
}

// Notification Card Component
interface NotificationCardProps {
  notification: Notification;
  isSelected: boolean;
  onSelect: (checked: boolean) => void;
  onClick: () => void;
}

function NotificationCard({
  notification,
  isSelected,
  onSelect,
  onClick,
}: NotificationCardProps) {
  const getTemplateIcon = () => {
    return <FileText className="h-4 w-4" />;
  };

  const formatDate = (date: string | Date) => {
    try {
      const dateObj = typeof date === "string" ? new Date(date) : date;
      return formatDistanceToNow(dateObj, { addSuffix: true });
    } catch {
      return "Unknown date";
    }
  };

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't trigger onClick if clicking on checkbox
    if ((e.target as HTMLElement).closest('[role="checkbox"]')) {
      return;
    }
    onClick();
  };

  return (
    <Card
      className={`cursor-pointer transition-all hover:shadow-md ${
        !notification.isRead ? "border-primary/30 bg-primary/5" : ""
      } ${isSelected ? "ring-2 ring-primary/50" : ""}`}
      onClick={handleCardClick}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          {/* Checkbox */}
          <Checkbox
            checked={isSelected}
            onCheckedChange={onSelect}
            onClick={(e) => e.stopPropagation()}
            className="mt-1"
          />

          {/* Status Indicator */}
          <div className="mt-1">
            {notification.isRead ? (
              <Circle className="h-3 w-3 text-muted-foreground" />
            ) : (
              <Circle className="h-3 w-3 text-primary fill-current" />
            )}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2 mb-2">
              <h3
                className={`font-semibold break-words ${
                  !notification.isRead
                    ? "text-foreground"
                    : "text-muted-foreground"
                }`}
              >
                {notification.title}
              </h3>
              <div className="flex items-center gap-2 flex-shrink-0">
                {notification.pdfData?.templateName && (
                  <Badge variant="outline" className="text-xs">
                    {getTemplateIcon()}
                    <span className="ml-1 hidden sm:inline">
                      {notification.pdfData.templateName}
                    </span>
                  </Badge>
                )}
                <span className="text-xs text-muted-foreground">
                  {formatDate(notification.createdAt)}
                </span>
              </div>
            </div>

            <p
              className={`text-sm break-words ${
                !notification.isRead
                  ? "text-foreground"
                  : "text-muted-foreground"
              }`}
            >
              {notification.message}
            </p>

            {/* Additional Info */}
            <div className="flex items-center gap-4 mt-3 text-xs text-muted-foreground">
              {notification.type && (
                <Badge variant="secondary" className="text-xs">
                  {notification.type}
                </Badge>
              )}

              {notification.pdfData?.status && (
                <Badge
                  variant={
                    notification.pdfData.status === "approved"
                      ? "default"
                      : "outline"
                  }
                  className={`text-xs ${
                    notification.pdfData.status === "approved"
                      ? "bg-green-100 text-green-800 border-green-200"
                      : "bg-yellow-100 text-yellow-800 border-yellow-200"
                  }`}
                >
                  {notification.pdfData.status === "approved"
                    ? "Approved"
                    : "Pending"}
                </Badge>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
